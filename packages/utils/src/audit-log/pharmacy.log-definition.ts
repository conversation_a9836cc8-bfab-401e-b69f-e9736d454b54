import { z } from 'zod';

export const pharmacyAuditLogDefinitions = [
  z.object({
    action: z.literal('PRESCRIPTION_TRANSFERRED'),
    details: z.object({
      state: z.enum(['success', 'error']).optional(),
      doctorName: z.string().optional(),
      oldPharmacy: z.string().optional(),
      newPharmacy: z.string().optional(),
      productType: z.string().optional(),
      productForm: z.string().optional(),
    }),
  }),
];
