import { z } from 'zod';

import type { ActorExtraDetails } from './types';
import { chatAuditLogDefinitions } from './chat.log-definition';
import { followUpAuditLogDefinitions } from './follow-up.log-definition';
import { intercomAuditLogDefinitions } from './intercom.log-definition';
import { onboardingAuditLogDefinitions } from './onboarding.log-definition';
import { patientAuditLogDefinitions } from './patient.log-definition';
import { pharmacyAuditLogDefinitions } from './pharmacy.log-definition';
import { treatmentLogDefinitions } from './treatment.log-definition';
import {
  zAuditLogEntity,
  zSystemAuditLogActor,
  zUserAuditLogActor,
} from './types';

const zNewAuditLog = z.object({
  id: z.string().optional(),
  action: z.string(),
  actorType: zAuditLogEntity,
  actorId: z.string(),
  resourceType: zAuditLogEntity,
  resourceId: z.string(),
  patientId: z.string(),
  details: z.record(z.unknown()),
  createdAt: z.date().optional(),
});

const zAuditLogActor = z.discriminatedUnion('actorType', [
  zUserAuditLogActor,
  zSystemAuditLogActor,
]);

const zAuditLogAction = z.discriminatedUnion('action', [
  ...patientAuditLogDefinitions,
  ...onboardingAuditLogDefinitions,
  ...followUpAuditLogDefinitions,
  ...treatmentLogDefinitions,
  ...chatAuditLogDefinitions,
  ...intercomAuditLogDefinitions,
  ...pharmacyAuditLogDefinitions,
]);

export const zAuditLog = z.intersection(
  zNewAuditLog.omit({
    id: true,
    action: true,
    details: true,
    createdAt: true,
  }),
  zAuditLogAction,
  zAuditLogActor,
);

export type AuditLogActorEntity = z.infer<typeof zAuditLogEntity>;
export type AuditLogInsert = z.infer<typeof zAuditLog>;
export type AuditLog = AuditLogInsert & {
  id: string;
  actorExtraDetails: ActorExtraDetails;
};
export type AuditLogApi = AuditLogInsert & {
  id: string;
  actorExtraDetails: ActorExtraDetails;
  createdAt: string;
};

export interface UpdatedBy {
  type: AuditLogActorEntity;
  id: string;
}
