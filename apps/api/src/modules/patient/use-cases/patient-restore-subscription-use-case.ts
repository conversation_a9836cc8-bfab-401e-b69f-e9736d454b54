import { AuditService } from '@/modules/audit-log/audit-log.service';
import { roles } from '@/modules/auth/types/roles';
import { SendMessageUseCase } from '@/modules/chat/use-cases/send-message.use-case';
import { PrismaService } from '@/modules/prisma/prisma.service';
import {
  segmentIdentifyEvent,
  segmentTrackEvents,
} from '@/modules/shared/events';
import { SegmentIdentify, SegmentTrack } from '@/modules/shared/types/events';
import { BadRequestException, Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';

@Injectable()
export class PatientRestoreSubscriptionUseCase {
  constructor(
    private readonly sendMessageUseCase: SendMessageUseCase,
    private readonly prismaService: PrismaService,
    private readonly eventEmitter: EventEmitter2,
    private readonly auditService: AuditService,
  ) {}

  async execute({
    patientId,
    restoredBy,
  }: {
    patientId: string;
    restoredBy: {
      type: 'DOCTOR' | 'ADMIN' | 'PATIENT';
      userId: string;
      id: string;
    };
  }) {
    try {
      const patient = await this.prismaService.patient.findFirst({
        where: { id: patientId, status: 'cancelled' },
        select: {
          id: true,
          statusBeforeCancellation: true,
          verificationStatus: true,
          doctorId: true,
          userId: true,
          conversation: true,
        },
      });

      if (!patient) {
        throw new Error('only cancelled patients can be restored');
      }

      await this.prismaService.patient.update({
        where: { id: patient.id },
        data: {
          status: patient.statusBeforeCancellation,
          statusBeforeCancellation: null,
          cancelationNote: null,
          cancelationReason: null,
          canceledAt: null,
          canceledByUserId: null,
        },
      });

      const restoredByUserId =
        restoredBy.type === 'PATIENT' ? patient.userId : restoredBy.userId;

      if (restoredBy.type === 'PATIENT' && patient.conversation?.id) {
        void this.sendMessageUseCase.execute({
          conversationId: patient.conversation.id,
          userId: patient.userId,
          role: roles.Patient,
          content:
            'Hello I would like to restore my account and continue treatment',
          contentType: 'text',
          needsReply: false,
          type: 'message',
        });
      }

      void this.auditService.append({
        patientId: patientId,
        actorType: restoredBy.type,
        actorId: restoredBy.id,
        resourceType: 'PATIENT',
        resourceId: patientId,
        action: 'PATIENT_UNCANCELLED',
        details: {},
      });

      const identifyPatientEvent: SegmentIdentify = {
        userId: patient.userId,
        traits: {
          status: 'active',
        },
      };
      this.eventEmitter.emit(
        segmentIdentifyEvent.analyticIdentify,
        identifyPatientEvent,
      );

      const trackEventPatientUncanceled: SegmentTrack = {
        event: segmentTrackEvents.patientUncancelled.name,
        userId: patient.userId,
        properties: {
          restoredBy: restoredBy.type.toLocaleLowerCase(),
          restoredByUserId,
        },
      };
      this.eventEmitter.emit(
        segmentTrackEvents.patientUncancelled.event,
        trackEventPatientUncanceled,
      );
    } catch (e) {
      throw new BadRequestException(e.message);
    }
  }
}
