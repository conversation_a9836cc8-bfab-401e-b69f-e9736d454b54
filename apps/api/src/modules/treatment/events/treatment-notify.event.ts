import { SendMessageUseCase } from '@/modules/chat/use-cases/send-message.use-case';
import { SnsConsume } from '@/modules/shared/aws/sns/sns.decorator';
import { segmentTrackEvents } from '@/modules/shared/events';
import { TreatmentUpdatedQueueEvent } from '@/modules/shared/events/treatment-topic.definition';
import { SegmentTrack } from '@/modules/shared/types/events';
import { PrismaService } from '@modules/prisma/prisma.service';
import { replaceTemplate } from '@modules/shared/helpers/generic';
import {
  ActiveTreatmentProduct,
  TreatmentMachineContext,
  TreatmentProduct,
} from '@modules/treatment/states/treatment.state';
import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Product, ProductForms, ProductPrice } from '@prisma/client';

@Injectable()
export class TreatmentNotifyEventListener {
  // Record of form types to message templates
  private readonly messageTemplates: Record<ProductForms, string> = {
    injectable:
      'Hi, your next refill of {refill} will be processing in 2 days. Please use {dosageDescription} {dosageTimeframe} when you start the new vial. {dosageAdditionalMessage}',
    oral: 'Hi, your next refill of {refill} will be processing in 2 days. Please use {dosageDescription} {dosageTimeframe} when you start the new vial. {dosageAdditionalMessage}',
    tablet:
      'Hi, your next refill of {refill} will be processing in 2 days. Please use {dosageDescription} {dosageTimeframe}. {dosageAdditionalMessage}',
  };

  // Record of form types to update message templates
  private readonly updateMessageTemplates: Record<ProductForms, string> = {
    injectable:
      'Update regarding your next refill: Please use {dosageDescription} {dosageTimeframe} when you start the new vial. {dosageAdditionalMessage}',
    oral: 'Update regarding your next refill: Please use {dosageDescription} {dosageTimeframe} when you start the new vial. {dosageAdditionalMessage}',
    tablet:
      'Update regarding your next refill: Please use {dosageDescription} {dosageTimeframe}. {dosageAdditionalMessage}',
  };

  constructor(
    private readonly prisma: PrismaService,
    private readonly sendMessageUseCase: SendMessageUseCase,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  @SnsConsume({
    topic: 'treatment-updated',
    consumerGroup: 'treatment-notify-next-refill',
    filter: ['treatment_notify_nextRefill'],
  })
  async nextRefill({ payload }: TreatmentUpdatedQueueEvent) {
    const { context } = payload.treatment.state as {
      context: TreatmentMachineContext;
    };
    console.log('Next refill event');

    const activeProduct = context.activeProduct;

    const trackDosageNotification: SegmentTrack = {
      event: segmentTrackEvents.newRefillSoon.name,
      userId: context.patientId,
      properties: {
        productName: activeProduct.name,
        refillDate: context.nextRefillDate,
      },
    };
    this.eventEmitter.emit(
      segmentTrackEvents.newRefillSoon.event,
      trackDosageNotification,
    );
  }

  @SnsConsume({
    topic: 'treatment-updated',
    consumerGroup: 'treatment-notify-next-dose',
    filter: ['treatment_notify_nextRefill'],
  })
  async nextDose({ payload }: TreatmentUpdatedQueueEvent) {
    const { context } = payload.treatment.state as {
      context: TreatmentMachineContext;
    };
    console.log('Next dose event');

    const activeProduct = context.activeProduct;

    let nextProduct: Partial<TreatmentProduct>;
    let price: ProductPrice & { product: Product };

    // Check if this treatment has been transferred to another treatment
    if (payload.treatment.transferredTo) {
      // Fetch the transferred treatment
      const transferredTreatment = await this.prisma.treatment.findUnique({
        where: { id: payload.treatment.transferredTo },
        include: { initialProductPrice: { include: { product: true } } },
      });

      if (!transferredTreatment) {
        console.error(
          `Transferred treatment ${payload.treatment.transferredTo} not found`,
        );
        return;
      }

      // Use the first product from the transferred treatment
      price = transferredTreatment.initialProductPrice;

      // Make sure we can access the dose
      if (!price || !price.dosageDescription) return;

      nextProduct = { id: price.id, dose: price.dosageDescription };
    } else {
      // Use the next product from the current treatment
      nextProduct = context.products[context.currentRefill + 1];

      // don't send notification if next product is not found
      if (!nextProduct) return;

      price = await this.prisma.productPrice.findFirst({
        where: { id: nextProduct.id },
        include: { product: true },
      });

      if (!price) return;
    }
    const { conversationId, doctorUserId } =
      await this.getConversationAndPatientName(context.patientId);

    const message: string = this.generateRefillMessage(price, activeProduct);

    await this.sendMessageUseCase.execute({
      content: message,
      contentType: 'text',
      role: 'Doctor',
      conversationId,
      userId: doctorUserId,
      type: 'system',
    });

    // send segment event
    const trackDosageNotification: SegmentTrack = {
      event: segmentTrackEvents.dosageNotification.name,
      userId: context.patientId,
      properties: {
        productName: activeProduct.name,
        currentDose: activeProduct.dose,
        newDose: nextProduct.dose,
      },
    };
    this.eventEmitter.emit(
      segmentTrackEvents.dosageNotification.event,
      trackDosageNotification,
    );
  }

  @SnsConsume({
    topic: 'treatment-updated',
    consumerGroup: 'treatment-fix-next-refill',
    filter: ['treatment_fix_nextRefill'],
  })
  async fixNextDose({ payload }: TreatmentUpdatedQueueEvent) {
    const { context } = payload.treatment.state as {
      context: TreatmentMachineContext;
    };
    console.log('Fix next dose event');

    const activeProduct = context.activeProduct;

    // We need to get the transferred treatment to calculate the new dose
    if (!payload.treatment.transferredTo) {
      console.error('No transferred treatment found for fix next dose event');
      return;
    }

    // Fetch the transferred treatment
    const transferredTreatment = await this.prisma.treatment.findUnique({
      where: { id: payload.treatment.transferredTo },
      include: { initialProductPrice: { include: { product: true } } },
    });

    if (!transferredTreatment) {
      console.error(
        `Transferred treatment ${payload.treatment.transferredTo} not found`,
      );
      return;
    }

    // Use the first product from the transferred treatment
    const price = transferredTreatment.initialProductPrice;

    // Make sure we can access the dose
    if (!price || !price.dosageDescription) {
      console.error('No dosage description found for transferred treatment');
      return;
    }

    const nextProduct = { id: price.id, dose: price.dosageDescription };

    // Get conversation info
    const { conversationId, doctorUserId } =
      await this.getConversationAndPatientName(context.patientId);

    // Calculate the new message (same format as in nextDose method)
    const newMessage = this.generateRefillMessage(price, activeProduct);

    // Get the last system message from the conversation
    const lastSystemMessage = await this.prisma.conversationMessage.findFirst({
      where: { conversationId, type: 'system' },
      orderBy: { createdAt: 'desc' },
    });

    // If the last system message is the same as the new message, no need to send an update
    if (lastSystemMessage && lastSystemMessage.content === newMessage) {
      console.log('New dose is the same as previous dose, no update needed');
      return;
    }

    // Generate update message using template system
    const updateMessage = this.generateUpdateMessage(price);

    await this.sendMessageUseCase.execute({
      content: updateMessage,
      contentType: 'text',
      role: 'Doctor',
      conversationId,
      userId: doctorUserId,
      type: 'system',
    });

    // Send segment event
    const trackDosageNotification: SegmentTrack = {
      event: segmentTrackEvents.dosageNotification.name,
      userId: context.patientId,
      properties: {
        productName: activeProduct.name,
        currentDose: activeProduct.dose,
        newDose: nextProduct.dose,
        isUpdate: true,
      },
    };
    this.eventEmitter.emit(
      segmentTrackEvents.dosageNotification.event,
      trackDosageNotification,
    );
  }

  private async getConversationAndPatientName(patientId: string) {
    const result = await this.prisma.patient.findFirst({
      where: { id: patientId },
      select: {
        conversation: { select: { id: true } },
        doctor: { select: { userId: true } },
      },
    });

    return {
      conversationId: result.conversation.id,
      doctorUserId: result.doctor.userId,
    };
  }

  private generateRefillMessage(
    price: ProductPrice & { product: Product },
    activeTreatmentProduct: ActiveTreatmentProduct,
  ) {
    const form = price.product.form;

    // Get the template for this form type
    const messageTemplate =
      this.messageTemplates[form] || this.messageTemplates.injectable;

    const templateData = {
      refill: activeTreatmentProduct.name,
      dosageDescription: price.dosageDescription,
      dosageTimeframe: price.dosageTimeframe,
      dosageAdditionalMessage: price.dosageAdditionalMessage || '',
    };

    return replaceTemplate(messageTemplate, templateData);
  }

  private generateUpdateMessage(price: ProductPrice & { product: Product }) {
    // Get the form from metadata, defaulting to 'injectable' if not found
    const form = price.product.form;

    // Get the update template for this form type
    const updateTemplate =
      this.updateMessageTemplates[form] ||
      this.updateMessageTemplates.injectable;

    const templateData = {
      dosageDescription: price.dosageDescription,
      dosageTimeframe: price.dosageTimeframe,
      dosageAdditionalMessage: price.dosageAdditionalMessage || '',
    };

    return replaceTemplate(updateTemplate, templateData).trim();
  }
}
