import { unknownIsObject } from '@/modules/shared/helpers/generic';
import {
  segmentIdentifyEvent,
  segmentTrackEvents,
} from '@modules/shared/events';
import { SegmentIdentify, SegmentTrack } from '@modules/shared/types/events';
import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';

import { SegmentService } from '../segment.service';

@Injectable()
export class ListenersService {
  constructor(private readonly segmentService: SegmentService) {}

  @OnEvent(segmentTrackEvents.disputeFiled.event)
  async handleDisputeCreated(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }
  @OnEvent(segmentTrackEvents.emailUpdated.event)
  async handleEmailUpdated(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.signUp.event)
  async handleSignUp(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.onboardingCompleted.event)
  async handleOnboardingCompleted(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.productPurchase.event)
  async handleProductPurchase(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.chargeRefunded.event)
  async handleChargeRefunded(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.patientAccepted.event)
  async handleSegmentTrack(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.identityAccepted.event)
  async handlePatientIdentityAccepted(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.identityRejected.event)
  async handlePatientIdentityRejected(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  // Lead Generated
  @OnEvent(segmentTrackEvents.leadGenerated.event)
  async handleLeadGenerated(payload: SegmentTrack) {
    //TODO: Add logic to handle lead generated event

    await this.segmentService.track(payload);
  }

  // Onboarding Step 1 Load
  @OnEvent(segmentTrackEvents.onbardingStarted.event)
  async handleOnboardingStarted(payload: SegmentTrack) {
    //TODO: Add logic to handle onboarding started event

    await this.segmentService.track(payload);
  }

  // User Enters State
  @OnEvent(segmentTrackEvents.stateSelected.event)
  async handleStateSelected(payload: SegmentTrack) {
    // TODO: Add logic to handle state selected event
    // Properties: state

    await this.segmentService.track(payload);
  }

  // User is Rejected from Onboarding
  @OnEvent(segmentTrackEvents.onboardingRejected.event)
  async handleOnboardingRejected(payload: SegmentTrack) {
    //TODO: Add logic to handle onboarding rejected event
    /**
     * Properties:
     * reasonQuestion - Question posed by the slide
     * reasonAnswer - Patient input that caused rejection
     * step - Onboarding Step the patient was on
     **/

    await this.segmentService.track(payload);
  }

  // User Joins State Waitlist - Identify and Track
  @OnEvent(segmentTrackEvents.waitlistJoined.event)
  async handleWaitlistJoinedTrack(trackPayload: SegmentTrack) {
    //NOTE: this is not a common condition is a typescript type assertion
    if (unknownIsObject(trackPayload.properties) && 'userId' in trackPayload) {
      const identifyPayload: SegmentIdentify = {
        userId: trackPayload.userId as string,
        traits: { ...trackPayload.properties },
      };
      await this.segmentService.identify(identifyPayload);
    }

    //TODO: Add logic to handle waitlist joined event
    /**
     * Properties:
     * state
     * email
     **/

    await this.segmentService.track(trackPayload);
  }

  // User successfully enters necessary info and creates their account - Track
  @OnEvent(segmentTrackEvents.visitStarted.event)
  async handleOnboardingVisitStartedTrack(payload: SegmentTrack) {
    //TODO: Add logic to handle visit started event

    await this.segmentService.track(payload);
  }

  // User completes the Account Creation segment of onboarding now starting their "Virual Visit". - Track
  @OnEvent(segmentTrackEvents.accountCreated.event)
  async handleAccountCreatedTrack(payload: SegmentTrack) {
    // TODO: Add logic to handle account created event

    await this.segmentService.track(payload);
  }

  // User completes the medical questionnaire section of onboarding, now moving to treatment selection - Track
  @OnEvent(segmentTrackEvents.questionnaireCompleted.event)
  async handleQuestionnaireCompletedTrack(payload: SegmentTrack) {
    // TODO: Add logic to handle questionnaire completed event

    await this.segmentService.track(payload);
  }

  // User Selects their treatment choice, including if they choose to have the doctor choose for them and clicks continue - Track
  @OnEvent(segmentTrackEvents.treatmentChosen.event)
  async handleTreatmentChosenTrack(payload: SegmentTrack) {
    // TODO: Add logic to handle treatment chosen event

    await this.segmentService.track(payload);
  }

  // User clicks continue starting the Identity verification section - Track
  @OnEvent(segmentTrackEvents.identityVerificationStarted.event)
  async handleIdentityVerificationStartedTrack(payload: SegmentTrack) {
    // TODO: Add logic to handle identity verification started event

    await this.segmentService.track(payload);
  }

  // User uploads their personal photo - Track
  @OnEvent(segmentTrackEvents.photoUploaded.event)
  async handlePhotoUploadedTrack(payload: SegmentTrack) {
    // TODO: Add logic to handle photo uploaded event

    await this.segmentService.track(payload);
  }

  // User uploads their id - Track
  @OnEvent(segmentTrackEvents.iDUploaded.event)
  async handleIDUploadedTrack(payload: SegmentTrack) {
    // TODO: Add logic to handle id uploaded event

    await this.segmentService.track(payload);
  }

  // User skips uploading their personal photo - Track
  @OnEvent(segmentTrackEvents.photoUploadSkipped.event)
  async handlePhotoUploadSkippedTrack(payload: SegmentTrack) {
    // TODO: Add logic to handle photo upload skipped event

    await this.segmentService.track(payload);
  }

  // User skips uploading their id - Track
  @OnEvent(segmentTrackEvents.iDUploadSkipped.event)
  async handleIDUploadSkippedTrack(payload: SegmentTrack) {
    //TODO: Add logic to handle id upload skipped event

    await this.segmentService.track(payload);
  }

  // User completes viewing their visit summary, clicking continue here - Track
  @OnEvent(segmentTrackEvents.visitSummary.event)
  async handleVisitSummaryTrack(payload: SegmentTrack) {
    // TODO: Add logic to handle visit summary event

    await this.segmentService.track(payload);
  }

  // User enters their shipping info successfully and clicks continue - Track
  @OnEvent(segmentTrackEvents.checkoutStarted.event)
  async handleCheckoutStartedTrack(payload: SegmentTrack) {
    // TODO: Add logic to handle checkout started event
    // Properties: productIDs (array of Stripe product IDs)
    await this.segmentService.track(payload);
  }

  // User successfully clicks submit and checks out - Track
  @OnEvent(segmentTrackEvents.checkoutComplete.event)
  async handleCheckoutCompleteTrack(payload: SegmentTrack) {
    //TODO: Add logic to handle checkout complete event
    /**
     * Properties:
     - productIDs (array of Stripe product IDs)
     - shippingAddress1
     - shippingAddress2
     - shippingCity
     - shippingState
     - shippingZipcode
     - state (from original choice in onboarding)
     - value
     * */
    await this.segmentService.track(payload);
  }

  // User successfully clicks submit and checks out - Identify
  // @OnEvent(segmentTrackEvents.checkoutComplete.event)
  // async handleCheckoutCompleteIdentify(payload: SegmentIdentify) {
  //     'SegmentController -> handleCheckoutComplete -> payload',
  //     payload,
  //   );
  //   //TODO: Add logic to handle checkout complete event
  //   /**
  //    * Properties:
  //     - shippingAddress1
  //     - shippingAddress2
  //     - shippingCity
  //     - shippingState
  //     - shippingZipcode
  //     - cardExpiration
  //     - cardExpirationTime (timestamp last day in month)
  //     - cardLast4
  //       - String
  //     - cardExpiration
  //       - String
  //       - 04/24
  //     - cardExpMonth
  //       - Int - 4, 6
  //     - cardExpYear
  //       - Int - 2028, 2027
  //     - cardExpirationTime (timestamp last day in month)
  //     - cardLast4
  //       - String - last 4 digits
  //     - cardType
  //       - Amex, Visa, Mastercard etc
  //     - billingAddress1
  //     - billingAddress2
  //     - billlingCity
  //     - billingState
  //     - billingZipcode
  //    */
  //   await this.segmentService.identify(payload);
  // }

  // User completes identity verification clicking continue here - Track
  @OnEvent(segmentTrackEvents.identityVerificationComplete.event)
  async handleIdentityVerificationCompleteTrack(payload: SegmentTrack) {
    //TODO: Add logic to handle identity verification complete event
    await this.segmentService.track(payload);
  }

  // medication prescribed.
  @OnEvent(segmentTrackEvents.medicationPrescribed.event)
  async handleMedicationPrescribedTrack(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.paymentProcess.event)
  async handlePaymentProcessedTrack(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.paymentFailed.event)
  async handlePaymentFailedTrack(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.invoiceUncollectible.event)
  async handleStripeInvoiceUncollectibleTrack(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  /**
   * Generic Identify event listener for segment events
   * @param payload  SegmentIdentify The segment Identify event to be sent to segment
   */
  @OnEvent(segmentIdentifyEvent.analyticIdentify)
  async handleSegmentIdentify(payload: SegmentIdentify) {
    await this.segmentService.identify(payload);
  }

  // Doctor sent Message - Track
  @OnEvent(segmentTrackEvents.doctorSentMessage.event)
  async handleDoctorSentMessageTrack(payload: SegmentTrack) {
    //TODO: Add logic to handle doctor sent message event
    /**
     * Properties:
     - doctorName
     - doctorID
     - patientName (First Last)
     - patientID
     * */
    await this.segmentService.track(payload);
  }

  // Patient sent Message - Track
  @OnEvent(segmentTrackEvents.patientSentMessage.event)
  async handlePatientSentMessageTrack(payload: SegmentTrack) {
    //TODO: Add logic to handle patient sent message event
    /**
     * Properties:
     - doctorName
     - doctorID
     - patientName (First Last)
     - patientID
     * */
    await this.segmentService.track(payload);
  }

  // Billing udpated - Track
  @OnEvent(segmentTrackEvents.billingUpdated.event)
  async handleBillingUpdatedTrack(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  // shippingaddress updated - Track
  @OnEvent(segmentTrackEvents.shippingUpdated.event)
  async handleShippingAdressUpdatedTrack(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.patientDeleted.event)
  async handlePatientDeletedTrack(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.dosageNotification.event)
  async handledosageNotificationTrack(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.newRefillSoon.event)
  async handleNewRefillSoonTrack(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.patientCancelled.event)
  async handlePatientCancelledTrack(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.patientUncancelled.event)
  async handlePatientUncancelledTrack(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.subscriptionCancelled.event)
  async handleSubscriptionCancelledTrack(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.treatmentTypeChosen.event)
  async handleSegmentTrackEventsTreatmentTypeChosenEvent(
    payload: SegmentTrack,
  ) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.followUpSent.event)
  async handleSegmentTrackEventsFollowUpSentEvent(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.followUpStarted.event)
  async handleSegmentTrackEventsFollowUpStartedEvent(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.followUpComplete.event)
  async handleSegmentTrackEventsFollowUpCompleteEvent(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.followUpCancelled.event)
  async handleSegmentTrackEventsFollowUpCancelledEvent(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.doctorCompleteFollowUp.event)
  async handleSegmentTrackEventsDoctorCompleteFollowUpEvent(
    payload: SegmentTrack,
  ) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.followUpNpsSurveyResponse.event)
  async handleSegmentTrackEventsFollowUpNpsSurveyResponseEvent(
    payload: SegmentTrack,
  ) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.patientReassigned.event)
  async handleSegmentTrackEventsPatientReassignedEvent(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.pharmacyMigrated.event)
  async handlePharmacyMigratedTrack(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.orderSent.event)
  async handleOrderSentEvent(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.patientDeleted.event)
  async handlePatientDeletedEvent(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.referralSent.event)
  async handleReferralSentEvent(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.referralLinkAccountCreated.event)
  async handleReferralLinkAccountCreatedEvent(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.referralReceived.event)
  async handleReferralReceivedEvent(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.referralLinkCheckoutComplete.event)
  async handleReferralLinkCheckoutCompleteEvent(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }

  @OnEvent(segmentTrackEvents.prescriptionTransferred.event)
  async handlePrescriptionTransferred(payload: SegmentTrack) {
    await this.segmentService.track(payload);
  }
}
