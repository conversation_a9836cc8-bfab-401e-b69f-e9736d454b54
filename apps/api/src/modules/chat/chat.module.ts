import { AuthModule } from '@modules/auth/auth.module';
import { ChatGateway } from '@modules/chat/chat.gateway';
import { CutomerioController } from '@modules/chat/cutomerio.controller';
import { CreateConversationUseCase } from '@modules/chat/use-cases/create-conversation.use-case';
import { CustomerioSendUseCase } from '@modules/chat/use-cases/customerio-send.use-case';
import { GetConversationMessagesUseCase } from '@modules/chat/use-cases/get-conversation-messages.use-case';
import { MarkAsReadUseCase } from '@modules/chat/use-cases/mark-as-read.use-case';
import { IntercomModule } from '@modules/intercom/intercom.module';
import { PrismaModule } from '@modules/prisma/prisma.module';
import { SqsModule } from '@modules/shared/aws/sqs/sqs.module';
import { OutboxerModule } from '@modules/shared/outboxer/outboxer.module';
import { SharedModule } from '@modules/shared/shared.module';
import { TreatmentModule } from '@modules/treatment/treatment.module';
import { forwardRef, Module } from '@nestjs/common';

import { AiModule } from '../ai/ai.module';
import { AuditLogModule } from '../audit-log/audit-log.module';
import { SnsModule } from '../shared/aws/sns/sns.module';
import { OrchestrationModule } from '../shared/orchestration/orchestration.module';
import { ChatController } from './chat.controller';
import { ChatWorker } from './chat.worker';
import { ChatImageService } from './services/chat.image.service';
import { PatientMessageRouterService } from './services/patient-message-router.service';
import { GetConversationWatcherUseCase } from './use-cases/get-conversation-watcher.use-case';
import { ImageUploadGetPreSignedUrlUseCase } from './use-cases/image-upload-get-pre-signed-url.use-case';
import { PatientMessageRouterQueue } from './use-cases/patient-message-router.queue';
import { SendMessageUseCase } from './use-cases/send-message.use-case';

@Module({
  imports: [
    forwardRef(() => AuthModule),
    PrismaModule,
    AuditLogModule,
    SharedModule,
    SqsModule,
    OutboxerModule,
    SnsModule,
    AiModule,
    IntercomModule,
    forwardRef(() => TreatmentModule),
    OrchestrationModule,
  ],
  providers: [
    ChatGateway,
    ChatImageService,
    ChatWorker,
    CreateConversationUseCase,
    CustomerioSendUseCase,
    GetConversationMessagesUseCase,
    GetConversationWatcherUseCase,
    ImageUploadGetPreSignedUrlUseCase,
    MarkAsReadUseCase,
    PatientMessageRouterQueue,
    PatientMessageRouterService,
    SendMessageUseCase,
  ],
  controllers: [ChatController, CutomerioController],
  exports: [
    CreateConversationUseCase,
    SendMessageUseCase,
    ChatImageService,
    PatientMessageRouterService,
  ],
})
export class ChatModule {}
