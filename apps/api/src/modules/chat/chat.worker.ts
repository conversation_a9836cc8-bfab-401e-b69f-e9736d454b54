import { PrismaService } from '@/modules/prisma/prisma.service';
import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';

import { OrchestrationService } from '../shared/orchestration/orchestration.service';

@Injectable()
export class ChatWorker {
  private readonly logger = new Logger(ChatWorker.name);
  private readonly disabled: boolean;

  constructor(
    private readonly prisma: PrismaService,
    private readonly orchestrationService: OrchestrationService,
  ) {
    this.disabled =
      process.env.IS_CLI === 'true' ||
      (process.env.ENVIRONMENT !== 'local' &&
        !!process.env.NEXT_PUBLIC_ENVIRONMENT);
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async fixMissingConversationsCron() {
    if (this.disabled) return;

    try {
      await this.orchestrationService.runWithLock(
        {
          lockKey: 'fixMissingConversationsCron-cron',
          ttl: 1000 * 60 * 4, // 5 minutes
          thisInstanceMustBePrimary: true,
        },
        async () => {
          await this.fixMissingConversations();
        },
      );
    } catch (error) {
      this.logger.error('Error in fixMissingConversationsCron', error);
    }
  }

  async fixMissingConversations() {
    const patients = await this.prisma.patient.findMany({
      where: {
        doctorId: { not: null },
        status: { notIn: ['cancelled', 'deleted'] },
        OR: [
          {
            conversation: null,
          },
          {
            conversation: {
              watcher: {
                // Less than 2 watchers
                none: {
                  id: {
                    not: undefined,
                  },
                  AND: {
                    id: {
                      not: undefined,
                    },
                  },
                },
              },
            },
          },
        ],
      },
      include: {
        user: true,
        doctor: true,
        conversation: {
          include: {
            watcher: true,
          },
        },
      },
    });

    this.logger.debug(
      `Found ${patients.length} patients with missing conversations`,
    );

    for (const patient of patients) {
      this.logger.debug(
        `Fixing patient ${patient.user.email} status ${patient.status}`,
      );

      // continue;
      await this.prisma.$transaction(async (tx) => {
        const conversation = await tx.conversation.findFirst({
          where: {
            userId: patient.userId,
          },
        });

        let conversationId = null;
        if (!conversation) {
          const c = await tx.conversation.create({
            data: {
              userId: patient.userId,
              patientId: patient.id,
            },
          });
          conversationId = c.id;
        } else {
          conversationId = conversation.id;
        }

        const watchers = await tx.conversationWatcher.findMany({
          where: { conversationId },
        });

        if (watchers.length < 2) {
          await tx.conversationWatcher.upsert({
            where: {
              conversationId_userId: {
                conversationId,
                userId: patient.userId,
              },
            },
            create: {
              userId: patient.userId,
              conversationId,
            },
            update: {},
          });

          await tx.conversationWatcher.upsert({
            where: {
              conversationId_userId: {
                conversationId,
                userId: patient.doctor.userId,
              },
            },
            create: {
              userId: patient.doctor.userId,
              conversationId,
            },
            update: {},
          });
        }
      });
    }
  }
}
