import { runInDbTransaction } from '@/helpers/transaction';
import { SnsConsume } from '@/modules/shared/aws/sns/sns.decorator';
import { Injectable, Logger } from '@nestjs/common';
import { subDays } from 'date-fns';

import { CacheService } from '../cache/cache.service';
import { PrismaService } from '../prisma/prisma.service';
import { StripeInvoiceUpdatedQueueEvent } from '../shared/events/invoice-topic.definition';
import { DraftInvoice, StripeService } from './service/stripe.service';

@Injectable()
export class StripeCreateInvoiceConsumer {
  private readonly logger = new Logger(StripeCreateInvoiceConsumer.name);

  constructor(
    private readonly stripe: StripeService,
    private cacheService: CacheService,
    private readonly prismaService: PrismaService,
  ) {}

  @SnsConsume({
    topic: 'stripe-invoice-updated',
    consumerGroup: 'stripe-create-invoice-2',
    maxRetries: 3,
    filter: ['create'],
  })
  async createInvoice({ payload }: StripeInvoiceUpdatedQueueEvent) {
    if (payload.event !== 'create') return;

    const trottleKey = `stripe:invoice-create-trottle:patientId_${payload.patientId}`;

    this.assertEveryTreatmentHasOnePrescription(payload.draftInvoice);
    await this.assertThrottleIsRespected(trottleKey, payload.draftInvoice);
    await this.assertPrescriptionsAreInvoicable(payload.draftInvoice);
    await this.assertPatientAccountIsActive(payload.patientId);
    await this.assertInvoiceNotAlreadySent(payload.draftInvoice);
    await this.assertLastInvoiceIsOldEnough(payload.draftInvoice);

    const { draftInvoice: draft } = payload;

    let invoiceId: string;
    try {
      const invoice = await this.stripe.createInvoice(draft.customerId, {
        description: draft.description,
        metadata: draft.metadata,
      });
      invoiceId = invoice.id;

      const prescriptionInvoiceItems: {
        prescriptionId: string;
        stripeInvoiceItemId: string;
        couponId?: string;
      }[] = [];
      for (const item of draft.items) {
        const invoiceItem = await this.stripe.addInvoiceItem({
          invoiceId: invoice.id,
          customerId: draft.customerId,
          item: item,
          couponId: item.couponId,
        });
        prescriptionInvoiceItems.push({
          prescriptionId: item.prescriptionId,
          stripeInvoiceItemId: invoiceItem.id,
        });
      }

      // apply coupons to the invoice
      const couponIds = draft.items
        .filter((item) => item.couponId)
        .map((item) => item.couponId);
      const invoiceCoupon = couponIds.length > 0 ? couponIds[0] : null;
      if (invoiceCoupon) {
        await this.stripe.applyCouponToInvoice(invoiceId, invoiceCoupon);
      }

      // finalize the invoice
      await runInDbTransaction(this.prismaService, async (prisma) => {
        for (const item of prescriptionInvoiceItems) {
          await prisma.prescription.update({
            where: { id: item.prescriptionId },
            data: {
              stripeInvoiceId: invoice.id,
              stripeInvoiceItemId: item.stripeInvoiceItemId,
              stripeCouponId: item.couponId,
              status: 'open',
            },
          });
        }

        // send the invoice to Stripe
        await this.stripe.attemptInvoiceCollect(invoiceId);
      });
    } catch (error) {
      console.error(
        `Error creating invoice ${draft.metadata.internalInvoiceId}`,
        error.message,
      );
      this.logger.error('Error creating invoice', error);
      if (invoiceId) {
        try {
          await this.stripe.deletDraft(invoiceId);
          await this.cacheService.del(trottleKey);
        } catch (deleteError) {
          this.logger.error('Error voiding invoice after failure', deleteError);
        }
      }
      throw error;
    }
  }

  assertEveryTreatmentHasOnePrescription(draftInvoice: DraftInvoice) {
    if (
      draftInvoice.metadata.prescriptionIdList.length !==
      draftInvoice.metadata.treatmentIdList.length
    ) {
      console.warn(
        `[createInvoiceConsumer] Draft invoice ${draftInvoice.metadata.internalInvoiceId} has not all prescriptions resolved`,
      );
      throw new Error(
        `Draft invoice ${draftInvoice.metadata.internalInvoiceId} has not all prescriptions resolved`,
      );
    }
  }

  async assertThrottleIsRespected(trottleKey: string, draft: DraftInvoice) {
    if (await this.cacheService.get(trottleKey)) {
      console.warn(
        `[createInvoiceConsumer] Invoice creation throttled for draft ${draft.metadata.internalInvoiceId}`,
      );
      throw new Error(
        `Invoice creation throttled for draft ${draft.metadata.internalInvoiceId}`,
      );
    }
    await this.cacheService.set(trottleKey, 'true', 30);
  }

  async assertPatientAccountIsActive(patientId: string) {
    const patient = await this.prismaService.patient.findUnique({
      where: { id: patientId },
      select: { status: true },
    });

    if (!patient) {
      console.warn(
        `[createInvoiceConsumer] Patient not found for patient ID ${patientId}`,
      );
      this.logger.error(`PATIENT_NOT_FOUND`, { patientId });
      throw new Error('Patient not found');
    }

    const INACTIVE_STATUSES = [
      'banned',
      'cancelled',
      'deleted',
      'onboardingRejected',
      'pendingApprovalFromDoctor',
    ];

    if (INACTIVE_STATUSES.includes(patient.status)) {
      console.warn(
        `[createInvoiceConsumer] Patient account is not active for patient ID ${patientId} - status: ${patient.status}`,
      );
      this.logger.error(`PATIENT_ACCOUNT_NOT_ACTIVE`, {
        patientId,
        status: patient.status,
      });
      throw new Error('Patient account is not active');
    }
  }

  async assertInvoiceNotAlreadySent(draft: DraftInvoice) {
    const internalInvoiceId = draft.metadata.internalInvoiceId;
    const invoices = await this.stripe.listInvoices({
      customerId: draft.customerId,
      limit: 1,
      metadata: {
        internalInvoiceId: { equals: draft.metadata.internalInvoiceId },
      },
    });

    if (invoices.data.length > 0) {
      console.warn(
        `[createInvoiceConsumer] Invoice already exists for internal invoice ID ${internalInvoiceId}`,
      );
      this.logger.error(`INVOICE_ALREADY_EXISTS`, {
        internalInvoiceId,
        foundInvoiceId: invoices.data[0].id,
      });
      throw new Error('Invoice already exists for this draft');
    }
  }

  async assertLastInvoiceIsOldEnough(draft: DraftInvoice) {
    const internalInvoiceId = draft.metadata.internalInvoiceId;
    const invoices = await this.stripe.listInvoices({
      customerId: draft.customerId,
      limit: 1,
    });

    if (invoices.data.length > 0) {
      const lastInvoice = invoices.data[0];
      const lastInvoiceDate = new Date(lastInvoice.created * 1000);

      if (['draft', 'uncollectible', 'void'].includes(lastInvoice.status)) {
        return;
      }

      const lastInvoicePriceIds = lastInvoice.lines.data
        .map((line) => line.price?.id)
        .filter((id) => !!id);

      if (lastInvoicePriceIds.length !== lastInvoice.lines.data.length) {
        console.warn(
          `[createInvoiceConsumer] Last invoice for draft ${internalInvoiceId} has missing price IDs`,
        );
        throw new Error(
          `[createInvoiceConsumer] Last invoice for draft ${internalInvoiceId} has missing price IDs`,
        );
      }

      const lastInvoiceHasSimilarProductsToDraft = draft.items.some((item) =>
        lastInvoicePriceIds.includes(item.priceId),
      );

      const twentyDaysAgo = subDays(new Date(), 20);
      if (lastInvoiceDate > twentyDaysAgo) {
        if (lastInvoice.paid && lastInvoiceHasSimilarProductsToDraft) {
          console.warn(
            `[createInvoiceConsumer] Last invoice for draft ${internalInvoiceId} is too recent`,
          );
          this.logger.error(`LAST_INVOICE_TOO_RECENT`, {
            internalInvoiceId,
            foundInvoiceId: lastInvoice.id,
          });
          throw new Error('Last invoice is too recent');
        } else if (lastInvoice.status === 'open') {
          // handle new invoice to retry paiment

          const prescription = await this.prismaService.prescription.findFirst({
            where: { stripeInvoiceId: lastInvoice.id },
          });

          if (!prescription) {
            console.warn(
              `[createInvoiceConsumer] found recent invoice for patient with no associated prescription`,
              `patientId: ${draft.metadata.patientId} - invoiceId: ${lastInvoice.id}`,
            );
            throw new Error(
              'found recent invoice for patient with no associated prescription',
            );
          }

          const lastOpenPrescription =
            await this.prismaService.prescription.findMany({
              where: {
                treatmentId: prescription.treatmentId,
                status: 'open',
                stripeInvoiceId: null,
              },
            });

          if (lastOpenPrescription.length === 1) {
            return;
          }

          console.warn(
            `[createInvoiceConsumer] no lastOpenPrescription`,
            `patientId: ${prescription.patientId} - invoiceId: ${lastInvoice.id}`,
          );
          throw new Error(`[createInvoiceConsumer] no lastOpenPrescription`);
        } else {
          console.warn(
            `[createInvoiceConsumer] Last invoice for draft ${internalInvoiceId} is too recent`,
          );
          throw new Error(
            `[createInvoiceConsumer] Last invoice for draft ${internalInvoiceId} is too recent`,
          );
        }
      }
    }
  }

  async assertPrescriptionsAreInvoicable(draft: DraftInvoice) {
    const prescriptionIds = draft.items.map((i) => i.prescriptionId);

    const prescriptions = await this.prismaService.prescription.findMany({
      where: {
        id: { in: prescriptionIds },
      },
      select: {
        id: true,
        status: true,
        stripeInvoiceId: true,
        treatment: {
          select: {
            id: true,
            status: true,
          },
        },
      },
    });

    if (prescriptionIds.length !== prescriptions.length) {
      console.warn(
        `[createInvoiceConsumer] Some prescriptions are not found for draft invoice ${draft.metadata.internalInvoiceId}`,
      );
      throw new Error(
        `Some prescriptions are not found for draft invoice ${draft.metadata.internalInvoiceId}`,
      );
    }

    for (const prescription of prescriptions) {
      if (prescription.status !== 'open') {
        console.warn(
          `[createInvoiceConsumer] Prescription ${prescription.id} is not open for draft invoice ${draft.metadata.internalInvoiceId}`,
        );
        throw new Error(
          `Prescription ${prescription.id} is not open for draft invoice ${draft.metadata.internalInvoiceId}`,
        );
      }

      if (prescription.stripeInvoiceId) {
        console.warn(
          `[createInvoiceConsumer] Prescription ${prescription.id} already has an invoice for draft invoice ${draft.metadata.internalInvoiceId}`,
        );
        throw new Error(
          `Prescription ${prescription.id} already has an invoice for draft invoice ${draft.metadata.internalInvoiceId}`,
        );
      }

      if (!prescription.treatment) {
        console.warn(
          `[createInvoiceConsumer] Prescription ${prescription.id} has no treatment for draft invoice ${draft.metadata.internalInvoiceId}`,
        );
        throw new Error(
          `Prescription ${prescription.id} has no treatment for draft invoice ${draft.metadata.internalInvoiceId}`,
        );
      }

      const treatmentStatusBlacklist = [
        'cancelled',
        'completed',
        'inProgress.waitingForPrescription',
      ];

      if (treatmentStatusBlacklist.includes(prescription.treatment.status)) {
        console.warn(
          `[createInvoiceConsumer] Treatment ${prescription.treatment.id} is not active for draft invoice ${draft.metadata.internalInvoiceId}`,
        );
        throw new Error(
          `Treatment ${prescription.treatment.id} is not active for draft invoice ${draft.metadata.internalInvoiceId}`,
        );
      }
    }
  }
}
