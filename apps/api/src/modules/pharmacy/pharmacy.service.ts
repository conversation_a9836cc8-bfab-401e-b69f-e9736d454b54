import { runInDbTransaction } from '@/helpers/transaction';
import {
  PrismaService,
  PrismaTransactionalClient,
} from '@modules/prisma/prisma.service';
import { segmentTrackEvents } from '@modules/shared/events';
import { SegmentTrack } from '@modules/shared/types/events';
import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Prisma } from '@prisma/client';

import { roles } from '../auth/types/roles';
import { SendMessageUseCase } from '../chat/use-cases/send-message.use-case';
import { DosespotService } from '../dosespot/dosespot.service';
import { NonRetriableError } from '../shared/errors/non-retriable.error';
import { RetriableError } from '../shared/errors/retriable.error';
import { TreatmentService } from '../treatment/services/treatment.service';
import { CreatePharmacyDto } from './dto/create-pharmacy.dto';
import { UpdatePharmacyDto } from './dto/update-pharmacy.dto';

type TreatmentWithRelations = Prisma.TreatmentGetPayload<{
  include: {
    initialProductPrice: {
      include: {
        product: {
          include: {
            pharmacy: true;
          };
        };
        equivalenceGroup: {
          include: {
            productPrices: {
              include: {
                product: true;
              };
            };
          };
        };
      };
    };
    topProductPrice: true;
    doctor: true;
    patient: true;
  };
}>;

@Injectable()
export class PharmacyService {
  private readonly logger = new Logger(PharmacyService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly dosespotService: DosespotService,
    private readonly treatmentService: TreatmentService,
    private readonly eventEmitter: EventEmitter2,
    private readonly sendMessageUseCase: SendMessageUseCase,
  ) {}

  async findAll(options?: {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    direction?: 'asc' | 'desc';
    showInactive?: boolean;
    showPatients?: boolean;
    inStateId?: string;
  }) {
    const {
      page = 1,
      limit = 10,
      search = '',
      sortBy = 'name',
      direction = 'asc',
      showInactive = false,
      showPatients = false,
      inStateId,
    } = options || {};

    // Create the where clause for filtering
    let where: Prisma.PharmacyWhereInput = {};

    // If showInactive is false, only show enabled pharmacies
    if (!showInactive) {
      where.enabled = true;
    }

    if (search) {
      where = {
        ...where, // Keep the enabled filter
        OR: [
          { name: { contains: search, mode: Prisma.QueryMode.insensitive } },
          {
            doseSpotPharmacyId: {
              contains: search,
              mode: Prisma.QueryMode.insensitive,
            },
          },
          {
            PharmacyOnState: {
              some: {
                state: {
                  OR: [
                    {
                      name: {
                        contains: search,
                        mode: Prisma.QueryMode.insensitive,
                      },
                    },
                    {
                      code: {
                        contains: search,
                        mode: Prisma.QueryMode.insensitive,
                      },
                    },
                  ],
                },
              },
            },
          },
        ],
      };
    }

    if (inStateId) {
      where = {
        ...where,
        PharmacyOnState: {
          ...(where.PharmacyOnState || {}),
          some: {
            stateId: inStateId,
          },
        },
      };
    }

    // Get total count for pagination
    const total = await this.prisma.pharmacy.count({ where });

    // Determine sort field and direction
    let orderBy: Prisma.PharmacyOrderByWithRelationInput = { name: direction };

    // Handle different sort fields
    if (
      sortBy === 'name' ||
      sortBy === 'doseSpotPharmacyId' ||
      sortBy === 'enabled' ||
      sortBy === 'regularPriority' ||
      sortBy === 'usingGLP1Priority' ||
      sortBy === 'color' ||
      sortBy === 'slug' ||
      sortBy === 'createdAt'
    ) {
      orderBy = { [sortBy]: direction };
    }

    // Handle legacy 'priority' sort field
    if (sortBy === 'priority') {
      orderBy = { regularPriority: direction };
    }

    // Fetch paginated pharmacies
    const pharmacies = await this.prisma.pharmacy.findMany({
      where,
      include: {
        PharmacyOnState: {
          include: { state: { select: { id: true, name: true, code: true } } },
        },
        Product: { select: { id: true, name: true } },
      },
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
    });

    // If showPatients is true, count active patients for each pharmacy
    if (showPatients) {
      const pharmaciesWithPatientCounts = await Promise.all(
        pharmacies.map(async (pharmacy) => {
          // Count patients with active treatments for this pharmacy
          const patientCount = await this.prisma.patient.count({
            where: {
              pharmacyId: pharmacy.id,
              user: { deletedAt: null },
            },
          });

          return {
            ...pharmacy,
            patientCount,
          };
        }),
      );

      return {
        pharmacies: pharmaciesWithPatientCounts,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      };
    }

    return {
      pharmacies,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string) {
    const pharmacy = await this.prisma.pharmacy.findUnique({
      where: { id },
      include: {
        PharmacyOnState: { include: { state: true } },
        Product: { orderBy: { order: 'asc' } },
      },
    });

    if (!pharmacy) return null;

    // Count active patients for this pharmacy
    const patientCount = await this.prisma.patient.count({
      where: {
        pharmacyId: pharmacy.id,
        user: { deletedAt: null },
      },
    });

    return {
      ...pharmacy,
      patientCount: patientCount,
    };
  }

  async create(data: CreatePharmacyDto) {
    const { stateIds, ...pharmacyData } = data;

    // Generate slug from name
    const slug = pharmacyData.name
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '');

    return this.prisma.pharmacy.create({
      data: {
        ...pharmacyData,
        slug,
        PharmacyOnState: {
          create:
            stateIds?.map((stateId) => ({
              stateId,
            })) || [],
        },
      },
      include: { PharmacyOnState: { include: { state: true } }, Product: true },
    });
  }

  async update(id: string, data: UpdatePharmacyDto) {
    const { stateIds, ...pharmacyData } = data;

    // If stateIds is provided, first delete existing relationships
    if (stateIds) {
      await this.prisma.pharmacyOnState.deleteMany({
        where: { pharmacyId: id },
      });
    }

    // Remove slug from update data to prevent editing it
    // Slug should only be set during creation

    return this.prisma.pharmacy.update({
      where: { id },
      data: {
        ...pharmacyData,
        ...(stateIds && {
          PharmacyOnState: {
            create: stateIds.map((stateId) => ({
              stateId,
            })),
          },
        }),
      },
      include: { PharmacyOnState: { include: { state: true } }, Product: true },
    });
  }

  async delete(id: string) {
    // Check if pharmacy exists
    const pharmacy = await this.findOne(id);

    if (!pharmacy) {
      throw new Error(`Pharmacy with ID ${id} not found`);
    }

    // Check if pharmacy has patients assigned to it
    const patientsCount = await this.prisma.patient.count({
      where: {
        pharmacyId: id,
      },
    });

    if (patientsCount > 0) {
      throw new Error(
        `Cannot delete pharmacy: ${patientsCount} patients are assigned to this pharmacy. Reassign these patients first.`,
      );
    }

    // First delete related records in PharmacyOnState
    await this.prisma.pharmacyOnState.deleteMany({
      where: { pharmacyId: id },
    });

    return this.prisma.pharmacy.delete({
      where: { id },
    });
  }

  async deactivate(id: string) {
    // Check if pharmacy exists
    const pharmacy = await this.findOne(id);

    if (!pharmacy) {
      throw new Error(`Pharmacy with ID ${id} not found`);
    }

    // Check if pharmacy has patients with active treatments
    const patientsWithActiveTreatments = await this.prisma.patient.count({
      where: {
        pharmacyId: id,
        treatment: {
          some: {
            // Active treatments are those that are not completed or uncollectible
            AND: [
              { status: { notIn: ['completed', 'uncollectible'] } },
              { deletedAt: null },
            ],
          },
        },
      },
    });

    if (patientsWithActiveTreatments > 0) {
      throw new Error(
        `Cannot deactivate pharmacy: ${patientsWithActiveTreatments} patients have active treatments`,
      );
    }

    return this.prisma.pharmacy.update({
      where: { id },
      data: { enabled: false },
      include: { PharmacyOnState: { include: { state: true } }, Product: true },
    });
  }

  async reactivate(id: string) {
    // Check if pharmacy exists
    const pharmacy = await this.findOne(id);

    if (!pharmacy) {
      throw new Error(`Pharmacy with ID ${id} not found`);
    }

    return this.prisma.pharmacy.update({
      where: { id },
      data: { enabled: true },
      include: { PharmacyOnState: { include: { state: true } }, Product: true },
    });
  }

  /**
   * Sends a doctor note to inform about a treatment during pharmacy transfer
   * @param prisma - Prisma transaction client
   * @param treatment - The treatment being transferred
   * @param targetPharmacyName - The name of the target pharmacy
   * @param wasCancelled - Whether the treatment was cancelled (true for paused) or transferred
   * @param vialCount - Optional vial count for multi-vial treatments that cannot be transferred
   */
  private async sendDoctorNoteForTreatmentTransfer(
    prisma: PrismaTransactionalClient,
    treatment: TreatmentWithRelations,
    targetPharmacyName: string,
    wasCancelled: boolean = false,
    vialCount?: number,
  ) {
    try {
      // Get the conversation and doctor information
      const patient = await prisma.patient.findFirst({
        where: { id: treatment.patientId },
        select: {
          conversation: { select: { id: true } },
          doctor: {
            select: {
              userId: true,
              user: { select: { firstName: true, lastName: true } },
            },
          },
        },
      });

      if (!patient?.conversation?.id || !patient?.doctor?.userId) {
        this.logger.warn(
          `Could not send doctor note: conversation or doctor not found for patient ${treatment.patientId}`,
        );
        return;
      }

      const conversationId = patient.conversation.id;
      const doctorUserId = patient.doctor.userId;
      const productName =
        treatment.initialProductPrice?.product?.name || 'medication';

      // Create the doctor note message based on treatment state and action
      let messageContent: string;

      if (vialCount && vialCount > 1) {
        // For multi-vial treatments that cannot be transferred
        messageContent = `Patient's treatment for ${productName} with ${vialCount} vials could not be transferred to ${targetPharmacyName} because there is no equivalent product with the same number of vials available at the new pharmacy. The remaining refills have been removed from the current treatment. Please prescribe a new treatment if the patient wishes to continue.`;
      } else if (wasCancelled) {
        messageContent = `Treatment for ${productName} was in paused state and has been cancelled during pharmacy transfer to ${targetPharmacyName}. The treatment could not be recreated in the new pharmacy because it was paused. The patient will need a new prescription if they wish to continue treatment.`;
      } else {
        // For active treatments that are being transferred
        messageContent = `Patient's active treatment for ${productName} has been transferred to ${targetPharmacyName}. The treatment was not yet prescribed and has been successfully moved to the new pharmacy with remaining refills preserved.`;
      }

      // Use the SendMessageUseCase to send the message
      await this.sendMessageUseCase.execute(
        {
          conversationId,
          userId: doctorUserId,
          content: messageContent,
          contentType: 'text',
          type: 'doctorNote',
          role: roles.Doctor,
          needsReply: false,
        },
        { prisma },
      );
    } catch (error) {
      // Log the error but don't throw it to avoid disrupting the pharmacy transfer process
      this.logger.error(
        `Error sending doctor note for treatment transfer: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Get patient counts grouped by state for a specific pharmacy
   * @param id Pharmacy ID
   * @returns Object with state information and patient counts
   */
  async getPatientCountsByState(id: string) {
    // Check if pharmacy exists
    const pharmacy = await this.prisma.pharmacy.findUnique({
      where: { id },
      include: {
        PharmacyOnState: {
          include: { state: true },
        },
      },
    });

    if (!pharmacy) {
      throw new Error(`Pharmacy with ID ${id} not found`);
    }

    // Get all states the pharmacy serves
    const stateIds = pharmacy.PharmacyOnState.map((pos) => pos.stateId);

    // Get the count of patients grouped by state in a single query for ALL states
    // (not just the ones currently associated with the pharmacy)
    const patientsByState = await this.prisma.patient.groupBy({
      by: ['stateId'],
      where: { pharmacyId: id },
      _count: { stateId: true },
    });

    // Create a map of state IDs to patient counts
    const stateCountMap = new Map(
      patientsByState.map((state) => [state.stateId, state._count.stateId]),
    );

    // Get all states that have patients but are not currently associated with the pharmacy
    const statesWithPatientsNotInPharmacy = patientsByState.filter(
      (state) => !stateIds.includes(state.stateId),
    );

    // Fetch details for these states
    let additionalStates = [];
    if (statesWithPatientsNotInPharmacy.length > 0) {
      const additionalStateIds = statesWithPatientsNotInPharmacy.map(
        (state) => state.stateId,
      );

      additionalStates = await this.prisma.state.findMany({
        where: { id: { in: additionalStateIds } },
      });
    }

    // Map the counts to state information for states associated with the pharmacy
    const statePatientCounts = [
      // First, include states that are currently associated with the pharmacy
      ...pharmacy.PharmacyOnState.map((stateInfo) => {
        const patientCount = stateCountMap.get(stateInfo.stateId) || 0;

        return {
          stateId: stateInfo.stateId,
          stateName: stateInfo.state.name,
          stateCode: stateInfo.state.code,
          patientCount,
          enabled: stateInfo.state.enabled,
          // Flag to indicate if this state is currently associated with the pharmacy
          isAssociated: true,
        };
      }),
      // Then, include states that have patients but are not associated with the pharmacy
      ...additionalStates.map((state) => {
        const patientCount = stateCountMap.get(state.id) || 0;

        return {
          stateId: state.id,
          stateName: state.name,
          stateCode: state.code,
          patientCount,
          enabled: state.enabled,
          // Flag to indicate this state is not currently associated with the pharmacy
          isAssociated: false,
        };
      }),
    ];

    // Get total count of patients for this pharmacy
    const totalPatientCount = await this.prisma.patient.count({
      where: {
        pharmacyId: id,
        user: { deletedAt: null },
      },
    });

    return {
      statePatientCounts,
      totalPatientCount,
    };
  }

  /**
   * Resolves the best pharmacy for a patient based on their GLP-1 status and state
   * This method also handles persisting the pharmacy ID to the patient record
   *
   * @param patientId - The patient's ID
   * @param stateId - The patient's state ID
   * @param form - The treatment form (e.g., 'injectable', 'oral')
   * @param onboardingSnapshot - The current onboarding snapshot
   * @returns The best pharmacy for the patient or null if no suitable pharmacy is found
   */
  async resolvePharmacyPriority(
    patientId: string,
    stateId: string,
    form: 'injectable' | 'oral' | 'tablet',
    onboardingSnapshot?: any,
  ) {
    // First check current pharmacy if it exists
    const currentPatient = await this.prisma.patient.findUnique({
      where: { id: patientId },
      select: { pharmacyId: true },
    });

    // Extract GLP-1 status from questionnaire context if available
    let usingGLP1: 'yes' | 'no' = 'no';
    if (onboardingSnapshot?.context?.questionnaire?.usingGLP1) {
      usingGLP1 = onboardingSnapshot.context.questionnaire.usingGLP1;
    }

    // Determine which priority field to use based on GLP-1 status
    const priorityField =
      usingGLP1 === 'yes' ? 'usingGLP1Priority' : 'regularPriority';

    // Build the where clause for filtering pharmacies
    const where: Prisma.PharmacyWhereInput = {
      enabled: true,
      PharmacyOnState: { some: { stateId } },
    };

    // Filter pharmacies that have active, core products available in onboarding with the specified form
    where.Product = {
      some: {
        form: { equals: form },
        active: true,
        isCore: true,
        isAvailableInOnboarding: true,
      },
    };

    // Find the pharmacy with the highest priority for the given criteria
    const pharmacy = await this.prisma.pharmacy.findFirst({
      where,
      orderBy: { [priorityField]: 'desc' },
      include: {
        PharmacyOnState: { include: { state: true } },
        Product: true,
      },
    });

    // If a pharmacy was found, update the patient's pharmacy in the database
    if (
      pharmacy &&
      (!currentPatient?.pharmacyId || currentPatient.pharmacyId !== pharmacy.id)
    ) {
      await this.prisma.patient.update({
        where: { id: patientId },
        data: { pharmacyId: pharmacy.id },
      });

      this.logger.log(
        `Updated patient ${patientId} with pharmacy ${pharmacy.name} (${pharmacy.id})`,
      );
    }

    return pharmacy;
  }

  /**
   * Transfers a patient to a new pharmacy
   * This method handles updating the patient's pharmacy in the database,
   * updating DoseSpot pharmacy association, and updating any related treatments.
   *
   * @param newPharmacyId - ID of the target pharmacy
   * @param patientId - ID of the patient to transfer
   * @param bulkTransferId - Optional ID of the bulk transfer this is part of
   * @param prisma
   * @returns A Promise that resolves to an object with success status, message, and transfer count
   * @throws NonRetriableError for validation or business logic errors
   * @throws RetriableError for database errors or DoseSpot API failures
   */
  async transferPatient(
    newPharmacyId: string,
    patientId: string,
    bulkTransferId?: string,
    prisma?: PrismaTransactionalClient,
  ) {
    // Use transaction to ensure all operations succeed or fail together
    return runInDbTransaction(prisma || this.prisma, async (tx) => {
      try {
        // Fetch patient with related data
        const patient = await tx.patient.findUnique({
          where: { id: patientId },
          include: {
            user: { select: { id: true, email: true } },
            doctor: {
              include: { user: true },
            },
            pharmacy: { select: { id: true, name: true } },
            state: { select: { id: true, code: true } },
          },
        });

        if (!patient) {
          throw new NonRetriableError('Patient not found');
        }

        // For new patients without a DoseSpot ID or doctor, we'll just update the pharmacy ID
        const isNewPatient = !patient.doseSpotPatientId || !patient.doctor;

        // Fetch target pharmacy with related data
        const targetPharmacy = await tx.pharmacy.findUnique({
          where: { id: newPharmacyId, enabled: true },
          include: {
            PharmacyOnState: {
              include: { state: true },
            },
          },
        });

        if (!targetPharmacy) {
          throw new NonRetriableError(
            'Target pharmacy not found or not enabled',
          );
        }

        // Check if the patient's state is allowed for the target pharmacy
        const allowedStates = new Set(
          targetPharmacy.PharmacyOnState.map((pos) => pos.stateId),
        );

        if (!allowedStates.has(patient.state.id)) {
          throw new NonRetriableError(
            `Target pharmacy does not service the patient's state`,
          );
        }

        // Check if the patient is already assigned to this pharmacy
        if (patient.pharmacy?.id === targetPharmacy.id) {
          throw new NonRetriableError(
            `Patient ${patientId} is already assigned to pharmacy ${newPharmacyId}. No transfer needed.`,
          );
        }

        // 2. Update patient's pharmacy in database (we'll set DoseSpot pharmacy after all DB operations)
        await tx.patient.update({
          where: { id: patientId },
          data: { pharmacyId: newPharmacyId },
        });

        // 3. Find all active treatments with remaining refills
        const activeTreatments = await tx.treatment.findMany({
          where: {
            patientId,
            status: { notIn: ['completed', 'cancelled', 'uncollectible'] },
            // We'll filter for remaining refills in the code
          },
          include: {
            initialProductPrice: {
              include: {
                product: {
                  include: {
                    pharmacy: true,
                  },
                },
                equivalenceGroup: {
                  include: {
                    productPrices: {
                      include: {
                        product: true,
                      },
                    },
                  },
                },
              },
            },
            topProductPrice: true,
            doctor: true,
            patient: true,
          },
        });

        const transferResults = [];

        // Filter treatments with remaining refills
        const treatmentsWithRemainingRefills = activeTreatments.filter(
          (treatment) =>
            treatment.currentRefill < treatment.refills ||
            treatment.status === 'paused',
        );

        // 4. Process each active treatment
        for (const treatment of treatmentsWithRemainingRefills) {
          try {
            // Get the actor for the treatment to extract necessary information BEFORE cancellation
            const treatmentEventsToEmit: {
              event: any;
            }[] = [];

            // Get the actor for the treatment
            const actor = await this.treatmentService.getActor(
              treatment.id,
              (e) => treatmentEventsToEmit.push(e),
              treatment.state as any,
            );

            if (
              !actor
                .getSnapshot()
                .can({ type: 'transfer', treatmentId: treatment.id })
            ) {
              this.logger.warn(
                `Treatment ${treatment.id} cannot be transferred`,
              );
              continue;
            }

            // Extract all necessary information BEFORE cancellation
            const actorSnapshot = actor.getSnapshot();
            const context = actorSnapshot.context;
            const treatmentState = this.treatmentService.getState(actor);
            const isScheduled = treatmentState === 'scheduled';
            const isPaused = treatmentState === 'paused';

            if (
              !context ||
              !context.products ||
              !Array.isArray(context.products)
            ) {
              throw new NonRetriableError(
                `Treatment ${treatment.id} has invalid state context`,
              );
            }

            // If the treatment is in paused state
            // just cancel it and skip creating a new one
            if (isPaused) {
              this.logger.log(`Cancelling paused treatment ${treatment.id}`);
              actor.send({ type: 'cancel' });

              // Update the treatment record
              await this.treatmentService.updateTreatmentRecord(actor, {
                prisma: tx,
              });

              // Emit events
              for (const { event } of treatmentEventsToEmit) {
                await this.treatmentService.emitTreatmentUpdatedEvent(
                  event,
                  treatment.id,
                  { prisma: tx },
                );
              }

              // Send a doctor note to inform about the cancelled paused treatment
              await this.sendDoctorNoteForTreatmentTransfer(
                tx,
                treatment,
                targetPharmacy.name,
                true, // wasCancelled = true for paused treatments
              );

              transferResults.push({
                success: true,
              });

              continue;
            }

            // Check if treatment has multiple vials
            // If so, strip refills and send doctor note instead of creating new treatment
            if (context.vials && context.vials > 1) {
              this.logger.log(
                `Treatment ${treatment.id} has ${context.vials} vials, stripping refills and sending doctor note`,
              );

              // Strip remaining refills by sending transfer event
              actor.send({ type: 'transfer', treatmentId: treatment.id });

              // Update the treatment record
              await this.treatmentService.updateTreatmentRecord(actor, {
                prisma: tx,
              });

              // Emit events
              for (const { event } of treatmentEventsToEmit) {
                await this.treatmentService.emitTreatmentUpdatedEvent(
                  event,
                  treatment.id,
                  { prisma: tx },
                );
              }

              // Send doctor note about no equivalence for multi-vial treatment
              await this.sendDoctorNoteForTreatmentTransfer(
                tx,
                treatment,
                targetPharmacy.name,
                false, // wasCancelled = false
                context.vials, // vialCount for multi-vial message
              );

              transferResults.push({ success: true });

              continue;
            }

            // Determine which product price to use based on treatment state
            let nextProductPriceId;

            // For scheduled treatments, we use the initial product price
            // For in-progress treatments, we use the next product price based on current refill
            if (isScheduled) {
              // For scheduled treatments, use the first product in the list
              const initialProductPrice = context.products[0];
              if (!initialProductPrice || !initialProductPrice.id) {
                throw new NonRetriableError(
                  `Treatment ${treatment.id} has invalid initial product price`,
                );
              }
              //
              nextProductPriceId = initialProductPrice.id;
            } else {
              // Get the next product price ID based on current refill
              // If currentRefill is 2, we need the product at index 3 for the next refill
              const nextRefillIndex = treatment.currentRefill + 1;

              // Make sure we don't go out of bounds
              if (nextRefillIndex >= context.products.length) {
                throw new NonRetriableError(
                  `Treatment ${treatment.id} has no more products in context`,
                );
              }

              const nextProductPrice = context.products[nextRefillIndex];

              if (!nextProductPrice || !nextProductPrice.id) {
                throw new NonRetriableError(
                  `Treatment ${treatment.id} has invalid next product price`,
                );
              }

              nextProductPriceId = nextProductPrice.id;
            }

            // For scheduled treatments, use the original delayUntil
            // For active treatments, use the nextRefillDate
            // This ensures that scheduled treatments maintain their original scheduled date
            // rather than using the calculated nextRefillDate which only applies to active treatments
            const delayUntil =
              isScheduled && context.delayUntil
                ? context.delayUntil
                : context.nextRefillDate;

            if (!delayUntil) {
              throw new NonRetriableError(
                `Treatment ${treatment.id} has no valid date for delayUntil`,
              );
            }

            // Find equivalent product price in the new pharmacy
            let newInitialProductPriceId = null;
            let newTopProductPriceId = null;

            // Find the equivalent product price in the new pharmacy
            const originalProductPrice = await tx.productPrice.findUnique({
              where: { id: nextProductPriceId },
              include: {
                equivalenceGroup: {
                  include: {
                    productPrices: {
                      include: {
                        product: true,
                      },
                    },
                  },
                },
              },
            });

            if (originalProductPrice?.equivalenceGroupId) {
              // Find equivalent product price using equivalence group
              const equivalentProductPrice =
                originalProductPrice.equivalenceGroup.productPrices.find(
                  (pp) => pp.product.pharmacyId === newPharmacyId,
                );

              if (equivalentProductPrice) {
                newInitialProductPriceId = equivalentProductPrice.id;
              }
            }

            if (!newInitialProductPriceId) {
              throw new NonRetriableError(
                `No equivalent product price found in target pharmacy for treatment ${treatment.initialProductPrice.name}`,
              );
            }

            // Handle top product price if it exists
            if (treatment.topProductPriceId) {
              const topProductPrice = await tx.productPrice.findUnique({
                where: { id: treatment.topProductPriceId },
                include: {
                  equivalenceGroup: {
                    include: {
                      productPrices: {
                        include: {
                          product: true,
                        },
                      },
                    },
                  },
                },
              });

              if (
                topProductPrice?.equivalenceGroupId &&
                topProductPrice.equivalenceGroup
              ) {
                const equivalentTopProductPrice =
                  topProductPrice.equivalenceGroup.productPrices.find(
                    (pp) => pp.product.pharmacyId === newPharmacyId,
                  );

                if (equivalentTopProductPrice) {
                  newTopProductPriceId = equivalentTopProductPrice.id;
                }
              }
            }

            // Calculate remaining refills
            // For scheduled treatments, use all refills since it hasn't started yet
            // For in-progress treatments, subtract the current refill and one more for the current one
            const remainingRefills = isScheduled
              ? treatment.refills
              : treatment.refills - treatment.currentRefill - 1;

            // 1. First create the new treatment with the new pharmacy using the treatment service
            const newTreatmentData =
              await this.treatmentService.createTreatment(
                {
                  patientId: treatment.patientId,
                  doctorId: treatment.doctorId,
                  pharmacyName: targetPharmacy.name,
                  refills: remainingRefills,
                  initialProductPriceId: newInitialProductPriceId,
                  finalProductPriceId: newTopProductPriceId,
                  vials: treatment.vials,
                  refillSystem: treatment.refillSystem as any,
                  notes: `Transferred from another pharmacy. Original treatment ID: ${treatment.id}`,
                  shortInitialPrescription: false, // Always false for transfers
                  delayUntil,
                },
                { prisma: tx },
              );

            // Emit prescriptionTransferred event for tracking
            try {
              const state = patient.state?.code || 'N/A';
              const doctorName =
                patient.doctor && (patient.doctor as any).user
                  ? `${(patient.doctor as any).user.firstName} ${(patient.doctor as any).user.lastName}`
                  : 'N/A';
              const oldPharmacy = patient.pharmacy?.name || 'N/A';
              const newPharmacy = targetPharmacy.name;
              let productType = 'Other';
              let productForm = 'injectable';
              const productMeta =
                (treatment.initialProductPrice as any)?.product?.metadata ||
                undefined;
              if (productMeta) {
                const type = (productMeta['label'] || '').toLowerCase();
                if (type === 'semaglutide') productType = 'Semaglutide';
                else if (type === 'tirzepatide') productType = 'Tirzepatide';
                const form = (productMeta['form'] || '').toLowerCase();
                if (form === 'oral') productForm = 'oral';
              }
              this.emitPrescriptionTransferredTrackEvent({
                treatment,
                state,
                doctorName,
                oldPharmacy,
                newPharmacy,
                productType,
                productForm,
              });
              // Register in the Activity Feed (AuditLog)
              await this.createPrescriptionTransferredAuditLog(tx, {
                treatment,
                doctorName,
                oldPharmacy,
                newPharmacy,
                productType,
                productForm,
                state,
              });
            } catch (err) {
              this.logger.error(
                'Failed to emit prescriptionTransferred event or log activity',
                err,
              );
            }

            // scheduled treatments should be cancelled immediately
            // in progress should continue and set to finish on current refill
            const type = isScheduled ? 'cancel' : 'transfer';
            actor.send({
              type,
              treatmentId: newTreatmentData.treatmentId,
            });

            // Update the treatment record
            const updatedTreatment =
              await this.treatmentService.updateTreatmentRecord(actor, {
                prisma: tx,
              });

            // Emit events
            for (const { event } of treatmentEventsToEmit) {
              await this.treatmentService.emitTreatmentUpdatedEvent(
                event,
                {
                  ...updatedTreatment,
                  transferredTo: newTreatmentData.treatmentId,
                },
                { prisma: tx },
              );
            }

            // 3. Update the original treatment with transferredTo reference
            await tx.treatment.update({
              where: { id: treatment.id },
              data: { transferredTo: newTreatmentData.treatmentId },
            });

            transferResults.push({ success: true });
          } catch (error) {
            // Any errors that happen in processing an individual treatment are non-retriable
            // We want to fail fast and make the admin fix the issue rather than retrying
            // Convert existing errors to NonRetriableError
            if (!(error instanceof NonRetriableError)) {
              throw new NonRetriableError(
                `Error transferring treatment ${treatment.id}: ${error.message}`,
              );
            }
            throw error;
          }
        }

        // Now that all SQL operations have completed successfully, update DoseSpot pharmacy
        // This should be the last operation, and if it fails, it should be retriable
        if (!isNewPatient) {
          try {
            await this.dosespotService.addPatientToPharmacy(
              patient.doseSpotPatientId,
              targetPharmacy.doseSpotPharmacyId,
              patient.doctor.doseSpotClinicianId,
            );
          } catch (error) {
            // If DoseSpot API call fails, throw a RetriableError to retry the whole operation
            throw new RetriableError(`DoseSpot API error: ${error.message}`);
          }

          // Emit segment event
          const pharmacyMigratedEvent: SegmentTrack = {
            event: segmentTrackEvents.pharmacyMigrated.name,
            userId: patient.user.id,
            properties: {
              from: patient.pharmacy?.name || 'No previous pharmacy',
              to: targetPharmacy.name,
            },
          };

          this.eventEmitter.emit(
            segmentTrackEvents.pharmacyMigrated.event,
            pharmacyMigratedEvent,
          );
        }

        // If this is part of a bulk transfer, increment the completedJobs counter
        if (bulkTransferId) {
          const updatedTransfer = await tx.bulkTransfer.update({
            where: {
              id: bulkTransferId,
              type: 'pharmacy',
            },
            data: { completedJobs: { increment: 1 } },
            select: { completedJobs: true, queuedJobs: true, status: true },
          });

          // Check if all jobs are completed and update status if needed
          if (updatedTransfer.completedJobs === updatedTransfer.queuedJobs) {
            await tx.bulkTransfer.update({
              where: {
                id: bulkTransferId,
                type: 'pharmacy',
              },
              data: {
                status: 'completed',
                completedAt: new Date(),
              },
            });
          }
        }

        return {
          success: true,
          message: `Patient transferred successfully to ${targetPharmacy.name}`,
          transferCount: transferResults.length,
        };
      } catch (error) {
        // Handle Prisma database errors as retriable
        if (
          error instanceof Prisma.PrismaClientKnownRequestError ||
          error instanceof Prisma.PrismaClientUnknownRequestError ||
          error instanceof Prisma.PrismaClientRustPanicError ||
          error instanceof Prisma.PrismaClientInitializationError
        ) {
          throw new RetriableError(
            `Database operation failed: ${error.message}`,
          );
        }

        // If it's a RetriableError, just rethrow it
        if (error instanceof RetriableError) {
          throw error;
        }

        // Re-throw if already classified as NonRetriableError
        if (error instanceof NonRetriableError) {
          throw error;
        }

        // By default, treat unclassified errors as non-retriable
        throw new NonRetriableError(`Unclassified error: ${error.message}`);
      }
    });
  }

  /**
   * Emits prescriptionTransferred event for Segment tracking
   */
  private emitPrescriptionTransferredTrackEvent({
    treatment,
    state,
    doctorName,
    oldPharmacy,
    newPharmacy,
    productType,
    productForm,
  }: {
    treatment: any;
    state: string;
    doctorName: string;
    oldPharmacy: string;
    newPharmacy: string;
    productType: string;
    productForm: string;
  }) {
    const status = 'Complete';
    const event: SegmentTrack = {
      event: segmentTrackEvents.prescriptionTransferred.name,
      userId: treatment.patientId,
      properties: {
        state,
        doctorName,
        oldPharmacy,
        newPharmacy,
        productType,
        productForm,
        status,
      },
    };
    this.eventEmitter.emit(
      segmentTrackEvents.prescriptionTransferred.event,
      event,
    );
  }

  /**
   * Creates an AuditLog entry for prescription transfer
   */
  private async createPrescriptionTransferredAuditLog(
    tx: PrismaTransactionalClient,
    {
      treatment,
      doctorName,
      oldPharmacy,
      newPharmacy,
      productType,
      productForm,
      state,
    }: {
      treatment: any;
      doctorName: string;
      oldPharmacy: string;
      newPharmacy: string;
      productType: string;
      productForm: string;
      state: string;
    },
  ) {
    await tx.auditLog.create({
      data: {
        actorType: 'SYSTEM',
        actorId: 'SYSTEM',
        resourceType: 'PATIENT',
        resourceId: treatment.patientId,
        action: 'PRESCRIPTION_TRANSFERRED',
        details: {
          state,
          doctorName,
          oldPharmacy,
          newPharmacy,
          productType,
          productForm,
        },
        patientId: treatment.patientId,
      },
    });
  }
}
